import PropTypes from 'prop-types';
import React, { useState, useEffect } from 'react';

// material-ui
import { useTheme } from '@mui/material/styles';
import Grid from '@mui/material/Grid2';
import MenuItem from '@mui/material/MenuItem';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';

// third party
import Chart from 'react-apexcharts';

// project imports
import SkeletonTotalGrowthBarChart from 'ui-component/cards/Skeleton/TotalGrowthBarChart';
import MainCard from 'ui-component/cards/MainCard';
import { gridSpacing } from 'store/constant';

const status = [
  {
    value: 'sales',
    label: 'Ventes'
  },
  {
    value: 'orders',
    label: 'Commandes'
  }
];

export default function SalesGrowthChart({ isLoading = false, data = [], error = null }) {
  const theme = useTheme();
  const [value, setValue] = useState('sales');
  const [chartData, setChartData] = useState(null);

  const { primary200, primaryDark, secondaryMain, secondaryLight, primary, darkLight, divider, grey500 } = theme.palette;

  useEffect(() => {
    console.log('📊 SalesGrowthChart: Received data:', data);

    // Always create a chart, even with empty data
    let categories, salesData, ordersData;

    if (!data || data.length === 0) {
      console.log('📊 SalesGrowthChart: No data available, using placeholder data');
      categories = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin'];
      salesData = [0, 0, 0, 0, 0, 0];
      ordersData = [0, 0, 0, 0, 0, 0];
    } else {
      categories = data.map((item) => item.month);
      salesData = data.map((item) => Number(item.sales) || 0);
      ordersData = data.map((item) => Number(item.orderCount) || 0);
    }

    console.log('📊 Chart categories:', categories);
    console.log('📊 Chart sales data:', salesData);
    console.log('📊 Chart orders data:', ordersData);

    const chartConfig = {
      height: 400,
      type: 'bar',
      options: {
        chart: {
          id: 'sales-chart',
          toolbar: {
            show: false
          },
          animations: {
            enabled: true
          }
        },
        xaxis: {
          categories: categories,
          labels: {
            style: {
              colors: grey500
            }
          }
        },
        yaxis: {
          labels: {
            style: {
              colors: grey500
            },
            formatter: function (val) {
              if (value === 'sales') {
                return val.toFixed(0) + ' DT';
              }
              return val.toString();
            }
          }
        },
        dataLabels: {
          enabled: false
        },
        tooltip: {
          y: {
            formatter: function (val) {
              if (value === 'sales') {
                return val.toFixed(2) + ' DT';
              }
              return val + ' commandes';
            }
          }
        },
        colors: [value === 'sales' ? primary : secondaryMain]
      },
      series: [
        {
          name: value === 'sales' ? 'Ventes (DT)' : 'Nombre de Commandes',
          data: value === 'sales' ? salesData : ordersData
        }
      ]
    };

    console.log('📊 Setting chart config:', chartConfig);
    setChartData(chartConfig);
  }, [data, value, primary, secondaryMain, grey500, theme.palette.mode]);

  if (error) {
    return (
      <MainCard>
        <Grid container spacing={gridSpacing}>
          <Grid size={12}>
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                minHeight: '300px',
                backgroundColor: '#ffebee',
                borderRadius: '8px',
                border: '2px solid #f44336'
              }}
            >
              <Typography variant="h6" sx={{ color: 'error.main', mb: 1 }}>
                ❌ Erreur de chargement
              </Typography>
              <Typography variant="body2" sx={{ color: 'error.dark', textAlign: 'center' }}>
                Impossible de charger les données de ventes: {error}
              </Typography>
            </div>
          </Grid>
        </Grid>
      </MainCard>
    );
  }

  return (
    <>
      {isLoading ? (
        <SkeletonTotalGrowthBarChart />
      ) : (
        <MainCard>
          <Grid container spacing={gridSpacing}>
            <Grid size={12}>
              <Grid container sx={{ alignItems: 'center', justifyContent: 'space-between' }}>
                <Grid>
                  <Grid container direction="column" spacing={1}>
                    <Grid>
                      <Typography variant="subtitle2">Évolution des Ventes</Typography>
                    </Grid>
                    <Grid>
                      <Typography variant="h3">
                        {data && data.length > 0
                          ? value === 'sales'
                            ? `${data.reduce((sum, item) => sum + (Number(item.sales) || 0), 0).toFixed(0)} DT`
                            : `${data.reduce((sum, item) => sum + (Number(item.orderCount) || 0), 0)} commandes`
                          : '0 DT'}
                      </Typography>
                    </Grid>
                  </Grid>
                </Grid>
                <Grid>
                  <TextField id="standard-select-currency" select value={value} onChange={(e) => setValue(e.target.value)}>
                    {status.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </TextField>
                </Grid>
              </Grid>
            </Grid>
            <Grid size={12}>
              <div style={{ width: '100%', minHeight: '400px' }}>
                {chartData ? (
                  <Chart options={chartData.options} series={chartData.series} type={chartData.type} height={chartData.height} />
                ) : (
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      minHeight: '400px',
                      backgroundColor: '#f5f5f5',
                      borderRadius: '8px',
                      border: '2px dashed #ddd'
                    }}
                  >
                    <Typography variant="h6" sx={{ color: 'grey.600', mb: 1 }}>
                      📊 Chargement du graphique...
                    </Typography>
                    <Typography variant="body2" sx={{ color: 'grey.500', textAlign: 'center' }}>
                      Veuillez patienter pendant le chargement des données.
                    </Typography>
                  </div>
                )}
              </div>
            </Grid>
          </Grid>
        </MainCard>
      )}
    </>
  );
}

SalesGrowthChart.propTypes = {
  isLoading: PropTypes.bool,
  data: PropTypes.array,
  error: PropTypes.string
};
